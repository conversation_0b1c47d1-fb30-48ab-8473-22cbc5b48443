import React from 'react';

const RepairPage = ({ repairManagement, currentLanguage, t, formatPrice }) => {
  const {
    // States
    showNewRepairModal,
    setShowNewRepairModal,
    showRepairCompletionModal,
    setShowRepairCompletionModal,
    showClientRecoveryModal,
    setShowClientRecoveryModal,
    showRepairInfoModal,
    setShowRepairInfoModal,
    showRepairActionsModal,
    setShowRepairActionsModal,
    selectedRepairForActions,
    setSelectedRepairForActions,
    newRepair,
    setNewRepair,
    repairClientFilter,
    setRepairClientFilter,
    repairDeviceFilter,
    setRepairDeviceFilter,
    repairStatusFilter,
    setRepairStatusFilter,
    repairDateFilter,
    setRepairDateFilter,
    filteredRepairs,
    
    // Functions
    generateNextRepairId,
    generateRepairBarcode,
    resetNewRepairForm,
    printRepairQRCode,
    printRepairBonPour,
    handleCreateNewRepair,
    handleCompleteRepair
  } = repairManagement;

  return (
    <div className={`repair-page lang-${currentLanguage}`}>
      <div className={`page-header ${currentLanguage !== 'ar' ? 'page-header-ltr-split' : ''}`}>
        <div className="page-title-section">
          <h1>🛠️ {currentLanguage === 'ar' ? 'إدارة الإصلاح' : currentLanguage === 'fr' ? 'Gestion des Réparations' : 'Repair Management'}</h1>
          <p>{currentLanguage === 'ar' ? 'سير عمل الإصلاح' : currentLanguage === 'fr' ? 'Flux de travail des réparations' : 'Repair Workflow'}</p>
        </div>
      </div>

      {/* Repair Action Buttons - Centered Row */}
      <div className="repair-actions-section centered-actions">
        <div className="repair-actions-grid centered-row">
          <div className="repair-action-card new-repair">
            <button
              className="repair-action-btn"
              onClick={() => {
                const newId = generateNextRepairId();
                const newBarcode = generateRepairBarcode(newId);
                setNewRepair({
                  ...newRepair,
                  id: newId,
                  barcode: newBarcode,
                  createdAt: new Date().toISOString()
                });
                setShowNewRepairModal(true);
              }}
            >
              <div className="repair-action-icon">🆕</div>
              <div className="repair-action-title">{t('newBonPour', 'بون بور جديد')}</div>
              <div className="repair-action-desc">{t('createNewRepairTicket', 'إنشاء تذكرة إصلاح جديدة')}</div>
            </button>
          </div>

          <div className="repair-action-card complete-repair">
            <button
              className="repair-action-btn"
              onClick={() => setShowRepairCompletionModal(true)}
            >
              <div className="repair-action-icon">✅</div>
              <div className="repair-action-title">{t('repairCompleted', 'إصلاح مكتمل')}</div>
              <div className="repair-action-desc">{t('markRepairAsCompleted', 'تحديد الإصلاح كمكتمل')}</div>
            </button>
          </div>

          <div className="repair-action-card client-recovery">
            <button
              className="repair-action-btn"
              onClick={() => setShowClientRecoveryModal(true)}
            >
              <div className="repair-action-icon">📱</div>
              <div className="repair-action-title">{t('clientRecovery', 'استلام من العميل')}</div>
              <div className="repair-action-desc">{t('clientDevicePickup', 'استلام الجهاز من العميل')}</div>
            </button>
          </div>
        </div>
      </div>

      {/* Repair Filters */}
      <div className="repair-filters-section">
        <div className="filters-row">
          <div className="filter-group">
            <label>{t('filterByClient', 'تصفية حسب العميل')}</label>
            <input
              type="text"
              value={repairClientFilter}
              onChange={(e) => setRepairClientFilter(e.target.value)}
              placeholder={t('clientName', 'اسم العميل')}
            />
          </div>

          <div className="filter-group">
            <label>{t('filterByDevice', 'تصفية حسب الجهاز')}</label>
            <input
              type="text"
              value={repairDeviceFilter}
              onChange={(e) => setRepairDeviceFilter(e.target.value)}
              placeholder={t('deviceName', 'اسم الجهاز')}
            />
          </div>

          <div className="filter-group">
            <label>{t('filterByStatus', 'تصفية حسب الحالة')}</label>
            <select
              value={repairStatusFilter}
              onChange={(e) => setRepairStatusFilter(e.target.value)}
            >
              <option value="">{t('allStatuses', 'جميع الحالات')}</option>
              <option value="In Process">{t('inProcess', 'قيد المعالجة')}</option>
              <option value="Waiting for Client">{t('waitingForClient', 'في انتظار العميل')}</option>
              <option value="Done">{t('done', 'مكتمل')}</option>
              <option value="Not Success">{t('notSuccess', 'غير ناجح')}</option>
            </select>
          </div>

          <div className="filter-group">
            <label>{t('filterByDate', 'تصفية حسب التاريخ')}</label>
            <input
              type="date"
              value={repairDateFilter}
              onChange={(e) => setRepairDateFilter(e.target.value)}
            />
          </div>

          <div className="filter-actions">
            <button
              className="btn btn-secondary"
              onClick={() => {
                setRepairClientFilter('');
                setRepairDeviceFilter('');
                setRepairStatusFilter('');
                setRepairDateFilter('');
              }}
            >
              🗑️ {t('clearFilters', 'مسح المرشحات')}
            </button>
          </div>
        </div>
      </div>

      {/* Repair Summary Cards */}
      <div className="repair-summary-cards-top">
        <div className="summary-card-item total-repairs">
          <div className="summary-icon">🛠️</div>
          <div className="summary-content">
            <div className="summary-number">{filteredRepairs.length}</div>
            <div className="summary-label">{t('totalRepairs', 'إجمالي الإصلاحات')}</div>
          </div>
        </div>

        <div className="summary-card-item completed-repairs">
          <div className="summary-icon">✅</div>
          <div className="summary-content">
            <div className="summary-number">
              {filteredRepairs.filter(repair => repair.situation === 'Done').length}
            </div>
            <div className="summary-label">{t('completedRepairs', 'الإصلاحات المكتملة')}</div>
          </div>
        </div>

        <div className="summary-card-item pending-repairs">
          <div className="summary-icon">⏳</div>
          <div className="summary-content">
            <div className="summary-number">
              {filteredRepairs.filter(repair => repair.situation === 'In Process' || repair.situation === 'Waiting for Client').length}
            </div>
            <div className="summary-label">{t('pendingRepairs', 'الإصلاحات المعلقة')}</div>
          </div>
        </div>

        <div className="summary-card-item repair-revenue">
          <div className="summary-icon">💰</div>
          <div className="summary-content">
            <div className="summary-number">
              {formatPrice(filteredRepairs.reduce((total, repair) =>
                total + (parseFloat(repair.repairPrice) || 0) - (parseFloat(repair.partsPrice) || 0), 0
              ))}
            </div>
            <div className="summary-label">{t('totalRepairRevenue', 'إجمالي إيرادات الإصلاح')}</div>
          </div>
        </div>
      </div>

      {/* Repair Table */}
      <div className="repair-table-section">
        <div className="table-container">
          <table className="data-table">
            <thead>
              <tr>
                <th>{t('qrCode', 'رمز QR')}</th>
                <th>{t('clientInfo', 'معلومات العميل')}</th>
                <th>{t('phoneNumber', 'رقم الهاتف')}</th>
                <th>{t('device', 'الجهاز')}</th>
                <th>{t('problem', 'المشكلة')}</th>
                <th>{t('repairPrice', 'سعر الإصلاح')}</th>
                <th>{t('totalPrice', 'السعر الإجمالي')}</th>
                <th>{t('status', 'الحالة')}</th>
                <th>{t('actions', 'الإجراءات')}</th>
              </tr>
            </thead>
            <tbody>
              {filteredRepairs.map((repair) => (
                <tr key={repair.id}>
                  <td>
                    <div
                      className="qr-cell"
                      onClick={() => printRepairQRCode(repair)}
                      title={t('clickToPrintQR', 'اضغط لطباعة رمز QR')}
                    >
                      <div className="qr-icon">📱</div>
                      <div className="repair-id">{repair.id}</div>
                    </div>
                  </td>
                  <td>
                    <div className="client-info">
                      <div className="client-name">{repair.clientName}</div>
                    </div>
                  </td>
                  <td>
                    <div className="phone-info">
                      <div className="phone-number">{repair.clientPhone || t('noPhone', 'لا يوجد هاتف')}</div>
                    </div>
                  </td>
                  <td>
                    <div className="device-info">
                      <div className="device-name">{repair.deviceName}</div>
                    </div>
                  </td>
                  <td>
                    <div className="problem-info">
                      <div className="problem-text">{repair.problem}</div>
                    </div>
                  </td>
                  <td>
                    <div className="price-cell">
                      <div className="price-amount">
                        {formatPrice(parseFloat(repair.repairPrice) || 0)}
                      </div>
                    </div>
                  </td>
                  <td>
                    <div className="price-cell">
                      <div className="total-price-amount">
                        {repair.repairFailed
                          ? formatPrice(parseFloat(repair.repairPrice) || 0)
                          : formatPrice((parseFloat(repair.repairPrice) || 0) - (parseFloat(repair.partsPrice) || 0))
                        }
                      </div>
                    </div>
                  </td>
                  <td>
                    <span className={`status-badge ${
                      repair.situation === 'In Process' ? 'status-in-process' :
                      repair.situation === 'Waiting for Client' ? 'status-waiting-for-client' :
                      repair.situation === 'Done' ? 'status-done' :
                      'status-not-success'
                    }`}>
                      {repair.situation === 'In Process' ? t('inProcess', 'قيد المعالجة') :
                       repair.situation === 'Waiting for Client' ? t('waitingForClient', 'في انتظار العميل') :
                       repair.situation === 'Done' ? t('done', 'مكتمل') :
                       t('notSuccess', 'غير ناجح')}
                    </span>
                  </td>
                  <td>
                    <div className="repair-actions-cell">
                      <button
                        className="repair-actions-btn modern-actions-btn"
                        onClick={() => {
                          setSelectedRepairForActions(repair);
                          setShowRepairActionsModal(true);
                        }}
                        title={t('actions', 'الإجراءات')}
                      >
                        <span className="actions-icon">⋮</span>
                        <span className="actions-text">{t('actions', 'الإجراءات')}</span>
                      </button>
                    </div>
                  </td>
                </tr>
              ))}

              {filteredRepairs.length === 0 && (
                <tr>
                  <td colSpan="9" style={{ textAlign: 'center', padding: '40px' }}>
                    <div className="no-data-message">
                      <div className="no-data-icon">🛠️</div>
                      <h3>{t('noRepairsFound', 'لا توجد إصلاحات')}</h3>
                      <p>{t('createFirstRepair', 'قم بإنشاء أول إصلاح باستخدام زر "بون بور جديد"')}</p>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default RepairPage;
